import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import {
    Dimensions,
    Image,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import Svg, { Circle, Path } from 'react-native-svg';

const { width } = Dimensions.get('window');

// Simple SVG Icons
const CreateIcon = () => (
  <Svg width={40} height={40} viewBox="0 0 24 24" fill="none">
    <Circle cx="12" cy="12" r="10" stroke="#6B46C1" strokeWidth="2" fill="none"/>
    <Path d="M12 8v8M8 12h8" stroke="#6B46C1" strokeWidth="2" strokeLinecap="round"/>
  </Svg>
);

const PersonalizeIcon = () => (
  <Svg width={40} height={40} viewBox="0 0 24 24" fill="none">
    <Circle cx="12" cy="12" r="3" stroke="#6B46C1" strokeWidth="2" fill="none"/>
    <Path d="M12 1v6M12 17v6M4.22 4.22l4.24 4.24M15.54 15.54l4.24 4.24M1 12h6M17 12h6M4.22 19.78l4.24-4.24M15.54 8.46l4.24-4.24" stroke="#6B46C1" strokeWidth="2" strokeLinecap="round"/>
  </Svg>
);

const ListenIcon = () => (
  <Svg width={40} height={40} viewBox="0 0 24 24" fill="none">
    <Path d="M9 18V5l12-2v13M9 18c0 1.66-1.34 3-3 3s-3-1.34-3-3 1.34-3 3-3 3 1.34 3 3zM21 16c0 1.66-1.34 3-3 3s-3-1.34-3-3 1.34-3 3-3 3 1.34 3 3z" stroke="#6B46C1" strokeWidth="2" fill="none"/>
  </Svg>
);

const WavePattern = () => (
  <Svg width={width} height={100} viewBox={`0 0 ${width} 100`} fill="none">
    <Path
      d={`M0,50 Q${width/4},20 ${width/2},50 T${width},50 L${width},100 L0,100 Z`}
      fill="white"
    />
  </Svg>
);

const Landing = () => {
  return (
    <SafeAreaView style={styles.safeArea}>
      <LinearGradient
        colors={['#6B46C1', '#9333EA', '#A855F7']}
        style={styles.gradient}
      >
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Header Section */}
          <View style={styles.headerSection}>
            <View style={styles.logoContainer}>
              <Image
                source={require('../assets/images/Logo_light_Readables.png')}
                style={styles.logo}
                resizeMode="contain"
              />
            </View>

            <Text style={styles.mainTitle}>
              Create an AI-enhanced, personal podcast from any source
            </Text>

            <Text style={styles.subtitle}>
              Podcast + AI = 🎧 Upload any content to create your readable at any time to ask questions or discuss the content with our AI assistant
            </Text>

            <View style={styles.buttonContainer}>
              <TouchableOpacity style={styles.primaryButton}>
                <Text style={styles.primaryButtonText}>Get started</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.secondaryButton}>
                <Text style={styles.secondaryButtonText}>Log in</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Wave decoration */}
          <View style={styles.waveContainer}>
            <WavePattern />
          </View>

          {/* How it works section */}
          <View style={styles.howItWorksSection}>
            <Text style={styles.sectionTitle}>How it works</Text>

            <View style={styles.stepsContainer}>
              {/* Create Step */}
              <View style={styles.stepContainer}>
                <View style={styles.iconContainer}>
                  <CreateIcon />
                </View>
                <Text style={styles.stepTitle}>Create</Text>
                <Text style={styles.stepDescription}>
                  Upload documents, paste text, or add website content to create your readable
                </Text>
              </View>

              {/* Personalize Step */}
              <View style={styles.stepContainer}>
                <View style={styles.iconContainer}>
                  <PersonalizeIcon />
                </View>
                <Text style={styles.stepTitle}>Personalize</Text>
                <Text style={styles.stepDescription}>
                  Choose from natural-sounding voices and adjust playback settings
                </Text>
              </View>

              {/* Listen Step */}
              <View style={styles.stepContainer}>
                <View style={styles.iconContainer}>
                  <ListenIcon />
                </View>
                <Text style={styles.stepTitle}>Listen</Text>
                <Text style={styles.stepDescription}>
                  Enjoy your content read aloud and interact with it through voice commands
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  headerSection: {
    paddingHorizontal: 24,
    paddingTop: 40,
    paddingBottom: 60,
    alignItems: 'center',
  },
  logoContainer: {
    marginBottom: 40,
  },
  logo: {
    width: 120,
    height: 40,
  },
  mainTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 34,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  primaryButton: {
    backgroundColor: 'white',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 24,
  },
  primaryButtonText: {
    color: '#6B46C1',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 24,
    borderWidth: 1,
    borderColor: 'white',
  },
  secondaryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  waveContainer: {
    marginTop: -20,
  },
  howItWorksSection: {
    backgroundColor: 'white',
    paddingHorizontal: 24,
    paddingTop: 40,
    paddingBottom: 60,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 40,
  },
  stepsContainer: {
    gap: 40,
  },
  stepContainer: {
    alignItems: 'center',
  },
  iconContainer: {
    width: 80,
    height: 80,
    backgroundColor: '#F3F4F6',
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 8,
  },
  stepDescription: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 22,
    maxWidth: 280,
  },
});

export default Landing;
