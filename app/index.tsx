import React from 'react';
import {
    Dimensions,
    Image,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import Svg, { Circle, Path } from 'react-native-svg';

const { width } = Dimensions.get('window');

// Simple SVG Icons
const CreateIcon = () => (
  <Svg width={40} height={40} viewBox="0 0 24 24" fill="none">
    <Circle cx="12" cy="12" r="10" stroke="#6B46C1" strokeWidth="2" fill="none"/>
    <Path d="M12 8v8M8 12h8" stroke="#6B46C1" strokeWidth="2" strokeLinecap="round"/>
  </Svg>
);

const PersonalizeIcon = () => (
  <Svg width={40} height={40} viewBox="0 0 24 24" fill="none">
    <Circle cx="12" cy="12" r="3" stroke="#6B46C1" strokeWidth="2" fill="none"/>
    <Path d="M12 1v6M12 17v6M4.22 4.22l4.24 4.24M15.54 15.54l4.24 4.24M1 12h6M17 12h6M4.22 19.78l4.24-4.24M15.54 8.46l4.24-4.24" stroke="#6B46C1" strokeWidth="2" strokeLinecap="round"/>
  </Svg>
);

const ListenIcon = () => (
  <Svg width={40} height={40} viewBox="0 0 24 24" fill="none">
    <Path d="M9 18V5l12-2v13M9 18c0 1.66-1.34 3-3 3s-3-1.34-3-3 1.34-3 3-3 3 1.34 3 3zM21 16c0 1.66-1.34 3-3 3s-3-1.34-3-3 1.34-3 3-3 3 1.34 3 3z" stroke="#6B46C1" strokeWidth="2" fill="none"/>
  </Svg>
);

const WavePattern = () => (
  <Svg width={width} height={100} viewBox={`0 0 ${width} 100`} fill="none">
    <Path
      d={`M0,50 Q${width/4},20 ${width/2},50 T${width},50 L${width},100 L0,100 Z`}
      fill="white"
    />
  </Svg>
);

const WaveBackground = () => {
  const { height } = Dimensions.get('window');
  return (
    <Svg
      width={width}
      height={height}
      viewBox="0 0 1440 684"
      fill="none"
      style={StyleSheet.absoluteFillObject}
    >
      <Path d="M0 0H1440V684H0V0Z" fill="#664BD8"/>
      <Path
        d="M140.476 121.801C537.461 278.548 343.968 152.129 1034.44 81.2578C1724.92 10.3866 1634.92 356.81 1866.91 195.865C2098.9 34.9202 1398.31 380.386 1398.31 380.386H-474C-474 380.386 -256.508 -34.9455 140.476 121.801Z"
        fill="#493BC6"
      />
      <Path
        d="M-404 261.65C-404 261.65 -193.322 332.939 49.4655 253.135C292.253 173.331 512.751 250.444 549.686 261.65C624.043 284.21 917.558 403.659 1026.53 261.65C1135.5 119.641 1549 145.141 1549 145.141L1503.37 489.956H-404V261.65Z"
        fill="#363076"
      />
      <Path
        d="M0 471.509H1476L1509.5 562.5C1408.5 577.143 1435 661 1193.61 620.234C952.225 579.467 446.388 788.556 321.85 609.833C197.312 431.109 104.575 599.869 0 583.495V471.509Z"
        fill="#363076"
      />
    </Svg>
  );
};

const Landing = () => {
  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.backgroundContainer}>
        <WaveBackground />
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Header Section */}
          <View style={styles.headerSection}>
            <View style={styles.logoContainer}>
              <Image
                source={require('../assets/images/Logo_light_Readables.png')}
                style={styles.logo}
                resizeMode="contain"
              />
            </View>

            <Text style={styles.mainTitle}>
              Create an AI-enhanced, personal podcast from any source
            </Text>

            <Text style={styles.subtitle}>
              Podcast + AI = 🎧 Upload any content to create your readable at any time to ask questions or discuss the content with our AI assistant
            </Text>

            <View style={styles.buttonContainer}>
              <TouchableOpacity style={styles.primaryButton}>
                <Text style={styles.primaryButtonText}>Get started</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.secondaryButton}>
                <Text style={styles.secondaryButtonText}>Log in</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Wave decoration */}
          <View style={styles.waveContainer}>
            <WavePattern />
          </View>

          {/* How it works section */}
          <View style={styles.howItWorksSection}>
            <Text style={styles.sectionTitle}>How it works</Text>

            <View style={styles.stepsContainer}>
              {/* Create Step */}
              <View style={styles.stepContainer}>
                <View style={styles.iconContainer}>
                  <CreateIcon />
                </View>
                <Text style={styles.stepTitle}>Create</Text>
                <Text style={styles.stepDescription}>
                  Upload documents, paste text, or add website content to create your readable
                </Text>
              </View>

              {/* Personalize Step */}
              <View style={styles.stepContainer}>
                <View style={styles.iconContainer}>
                  <PersonalizeIcon />
                </View>
                <Text style={styles.stepTitle}>Personalize</Text>
                <Text style={styles.stepDescription}>
                  Choose from natural-sounding voices and adjust playback settings
                </Text>
              </View>

              {/* Listen Step */}
              <View style={styles.stepContainer}>
                <View style={styles.iconContainer}>
                  <ListenIcon />
                </View>
                <Text style={styles.stepTitle}>Listen</Text>
                <Text style={styles.stepDescription}>
                  Enjoy your content read aloud and interact with it through voice commands
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  backgroundImage: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  headerSection: {
    paddingHorizontal: 24,
    paddingTop: 40,
    paddingBottom: 60,
    alignItems: 'center',
  },
  logoContainer: {
    marginBottom: 40,
  },
  logo: {
    width: 120,
    height: 40,
  },
  mainTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 34,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  primaryButton: {
    backgroundColor: 'white',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 24,
  },
  primaryButtonText: {
    color: '#6B46C1',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 24,
    borderWidth: 1,
    borderColor: 'white',
  },
  secondaryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  waveContainer: {
    marginTop: -20,
  },
  howItWorksSection: {
    backgroundColor: 'white',
    paddingHorizontal: 24,
    paddingTop: 40,
    paddingBottom: 60,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 40,
  },
  stepsContainer: {
    gap: 40,
  },
  stepContainer: {
    alignItems: 'center',
  },
  iconContainer: {
    width: 80,
    height: 80,
    backgroundColor: '#F3F4F6',
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 8,
  },
  stepDescription: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 22,
    maxWidth: 280,
  },
});

export default Landing;
